import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:smsautoforwardapp/controller/payment_controller.dart';
import 'package:smsautoforwardapp/forward_all.dart';
import 'package:smsautoforwardapp/rules.dart';
import 'package:smsautoforwardapp/style.dart';
import 'package:smsautoforwardapp/widgets/premium_ui.dart';
import 'package:permission_handler/permission_handler.dart';
import 'controller/auth_controller.dart';
import 'model/user.dart';

class Homepage extends StatefulWidget {
  const Homepage({
    super.key,
  });

  @override
  State<Homepage> createState() => _HomepageState();
}

class _HomepageState extends State<Homepage> {
  final AuthController authController = Get.find();
  final PaymentController paymentController = Get.put(PaymentController());

  @override
  void initState() {
    super.initState();
    _requestSMSPermission(); // Request SMS permission when the app starts
  }

  // Request SMS permission

  Future<void> _requestSMSPermission() async {
    // Check if the permission is already granted
    var status = await Permission.sms.status;
    if (!status.isGranted) {
      // ignore: use_build_context_synchronously
      showDialog(
        // ignore: use_build_context_synchronously
        context: context,
        builder: (BuildContext context) => AlertDialog(
          backgroundColor: Colors.white,
          title: const Text('SMS Permission'),
          content: const Text(
              "We need SMS permission to read incoming messages so that they can be forwarded to your preferred email or URL, and efficient backups can be created.\n\nThanks."),
          actions: <Widget>[
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Request the permission
                Permission.sms.request();
              },
              child: const Text('Allow'),
            ),
          ],
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: bgColor,
      resizeToAvoidBottomInset: false,
      body: SafeArea(
        child: Column(
          children: [
            SizedBox(
              height: Get.height * 0.03,
            ),
            appName,
            SizedBox(
              height: Get.height * 0.03,
            ),
            // Device limit warning
            Obx(
              () {
                bool isRestricted = authController.hasRestriction.value;
                if (isRestricted) {
                  return Container(
                    margin:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.orange.shade100,
                      border: Border.all(color: Colors.orange),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      children: [
                        const Row(
                          children: [
                            Icon(Icons.warning, color: Colors.orange),
                            SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                'Device limit reached! You can only use one device at a time with the current plan. Please upgrade to Elite Plan to use multiple devices.',
                                style: TextStyle(
                                  color: Colors.orange,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: () {
                              premiumBottomSheet(
                                context: context,
                                isModifyingSubscription: false,
                                forceElitePlan: true,
                              );
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.orange,
                              foregroundColor: Colors.white,
                            ),
                            child: const Text('Upgrade to Elite Plan'),
                          ),
                        ),
                      ],
                    ),
                  );
                }
                return const SizedBox.shrink();
              },
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(5.0),
                child: ListView(
                  children: [
                    Obx(
                      () => Card(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        elevation: 0,
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: ListTile(
                            onTap: authController.hasRestriction.value
                                ? null
                                : () {
                                    Get.to(() => const ForwardAll());
                                  },
                            enabled: authController.hasRestriction.value
                                ? false
                                : true,
                            title: txt(
                              txt: 'Forward All',
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              fontColor: blackishColor,
                            ),
                            subtitle: txt(
                                txt:
                                    'Set up a rule to forward all incoming messages',
                                maxLines: 10,
                                fontWeight: FontWeight.normal,
                                fontColor: blackishColor,
                                fontSize: 24),
                            trailing: const Padding(
                              padding: EdgeInsets.all(8.0),
                              child: Icon(
                                Icons.forward_outlined,
                                size: 36,
                                color: maincolor,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    Obx(
                      () => Card(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          elevation: 0,
                          child: Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: ListTile(
                                onTap: authController.hasRestriction.value
                                    ? null
                                    : () {
                                        Get.to(() => const ManageRules());
                                      },
                                enabled: authController.hasRestriction.value
                                    ? false
                                    : true,
                                title: txt(
                                  txt: 'Manage Rules',
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  fontColor: blackishColor,
                                ),
                                subtitle: txt(
                                    txt: 'Manage all your rules in one place',
                                    maxLines: 10,
                                    fontWeight: FontWeight.normal,
                                    fontColor: blackishColor,
                                    fontSize: 24),
                                trailing: const Padding(
                                  padding: EdgeInsets.all(8.0),
                                  child: Icon(
                                    Icons.rule,
                                    size: 36,
                                    color: maincolor,
                                  ),
                                ),
                              ))),
                    ),
                    Card(
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      elevation: 0,
                      child:
                          StreamBuilder<DocumentSnapshot<Map<String, dynamic>>>(
                        stream: FirebaseFirestore.instance
                            .collection('users')
                            .doc(authController.user!.uid)
                            .snapshots(),
                        builder: (context, snapshot) {
                          if (snapshot.data != null) {
                            final userData =
                                snapshot.data!.data() as Map<String, dynamic>;
                            final user = UserModel.fromJson(userData);

                            return Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: ListTile(
                                title: txt(
                                  txt: 'Forwards',
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  fontColor: blackishColor,
                                ),
                                subtitle: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    txt(
                                        txt: 'Forwards used this month',
                                        maxLines: 10,
                                        fontWeight: FontWeight.normal,
                                        fontColor: blackishColor,
                                        fontSize: 24),
                                    const SizedBox(
                                      height: 16,
                                    ),
                                    user.noOfForwardsused ==
                                                user.noOfForwardsPerMonth ||
                                            user.noOfForwardsused! >
                                                user.noOfForwardsPerMonth!
                                        ? Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              txt(
                                                  txt:
                                                      'Please upgrade subscription if you want more forwards per month.',
                                                  maxLines: 10,
                                                  fontWeight: FontWeight.w600,
                                                  fontColor: maincolor,
                                                  fontSize: 24),
                                              ElevatedButton(
                                                onPressed: () {
                                                  premiumBottomSheet(
                                                      context: context,
                                                      isModifyingSubscription:
                                                          false);
                                                },
                                                child: txt(
                                                  txt: 'Click here to upgrade',
                                                  fontStyle: FontStyle.italic,
                                                  fontSize: 16,
                                                  fontColor: Colors.white,
                                                ),
                                              ),
                                            ],
                                          )
                                        : Container()
                                  ],
                                ),
                                trailing: Padding(
                                  padding: const EdgeInsets.all(5.0),
                                  child: txt(
                                      txt:
                                          '${user.noOfForwardsused}/${user.noOfForwardsPerMonth}',
                                      maxLines: 10,
                                      fontWeight: FontWeight.w600,
                                      fontColor: maincolor,
                                      fontSize: 30),
                                ),
                              ),
                            );
                          } else {
                            return Container();
                          }
                        },
                      ),
                    )
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
